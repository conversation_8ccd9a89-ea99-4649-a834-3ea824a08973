<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SystemMessageType } from '#/api/system/message';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { Button, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteMessages,
  listUserMessageList,
  markMessageAsRead,
} from '#/api/system/message';

import { useColumns, useGridFormSchema } from './data';
import Detail from './modules/detail.vue';

const [DetailDrawer, detailDrawerApi] = useVbenDrawer({
  connectedComponent: Detail,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    checkboxConfig: {
      highlight: true,
      labelField: 'id',
    },
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await listUserMessageList({
            pageNum: page?.currentPage,
            pageSize: page?.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    events: {
      cellClick: ({ row, column }) => {
        // 如果点击的是复选框列，不触发详情查看
        if (column.type === 'checkbox') {
          return;
        }
        onRowClick(row);
      },
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: true,
      refreshOptions: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<SystemMessageType.UserMessageListVo>,
});

/**
 * 行点击事件 - 查看详情
 */
function onRowClick(row: SystemMessageType.UserMessageListVo) {
  detailDrawerApi.setData(row);
  detailDrawerApi.open();
}

function onRefresh() {
  gridApi.query();
}

/**
 * 批量删除消息
 */
function onBatchDelete() {
  const selectedRows = gridApi.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要删除的消息');
    return;
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRows.length} 条消息吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      const ids = selectedRows
        .map((row: SystemMessageType.UserMessageListVo) => {
          const id = row.id;
          return id;
        })
        .filter((id: any) => !Number.isNaN(id) && id !== undefined) as number[];

      const hideLoading = message.loading({
        content: `正在删除 ${selectedRows.length} 条消息...`,
        duration: 0,
        key: 'batch_delete_msg',
      });

      deleteMessages(ids)
        .then(() => {
          message.success({
            content: `成功删除 ${selectedRows.length} 条消息`,
            key: 'batch_delete_msg',
          });
          onRefresh();
        })
        .catch(() => {
          hideLoading();
        });
    },
  });
}

/**
 * 批量标记为已读
 */
function onBatchMarkRead() {
  const selectedRows = gridApi.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要标记的消息');
    return;
  }

  const unreadRows = selectedRows.filter((row) => row.isRead === 0);
  if (unreadRows.length === 0) {
    message.warning('选中的消息都已是已读状态');
    return;
  }

  const hideLoading = message.loading({
    content: `正在批量标记为已读...`,
    duration: 0,
    key: 'batch_mark_read_msg',
  });

  Promise.all(unreadRows.map((row) => markMessageAsRead(row.id!)))
    .then(() => {
      message.success({
        content: `成功标记 ${unreadRows.length} 条消息为已读`,
        key: 'batch_mark_read_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}
</script>

<template>
  <div>
    <DetailDrawer @success="onRefresh" />
    <Page auto-content-height>
      <Grid table-title="我的消息">
        <template #toolbar-tools>
          <Button @click="onBatchMarkRead"> 批量已读 </Button>
          <span class="mx-2"></span>
          <Button danger @click="onBatchDelete"> 批量删除 </Button>
        </template>
      </Grid>
    </Page>
  </div>
</template>