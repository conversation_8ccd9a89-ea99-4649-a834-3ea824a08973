<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { SystemMessageType } from '#/api/system/message';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteMessages,
  listUserMessageList,
  markMessageAsRead,
  markMessageAsUnRead,
} from '#/api/system/message';

import { useColumns, useGridFormSchema } from './data';
import Detail from './modules/detail.vue';

const [DetailModal, detailModalApi] = useVbenModal({
  connectedComponent: Detail,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    checkboxConfig: {
      highlight: true,
      labelField: 'id',
    },
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await listUserMessageList({
            pageNum: page?.currentPage,
            pageSize: page?.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: true,
      refreshOptions: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<SystemMessageType.UserMessageListVo>,
});

function onActionClick({
  code,
  row,
}: OnActionClickParams<SystemMessageType.UserMessageListVo>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'detail': {
      onDetail(row);
      break;
    }
    case 'markRead': {
      onMarkAsRead(row);
      break;
    }
    case 'markUnread': {
      onMarkAsUnread(row);
      break;
    }
    default: {
      break;
    }
  }
}

/**
 * 查看详情
 */
function onDetail(row: SystemMessageType.UserMessageListVo) {
  detailModalApi.setData(row);
  detailModalApi.open();
}

/**
 * 删除消息
 */
function onDelete(row: SystemMessageType.UserMessageListVo) {
  const hideLoading = message.loading({
    content: `正在删除消息...`,
    duration: 0,
    key: 'action_process_msg',
  });
  deleteMessages([row.id!])
    .then(() => {
      message.success({
        content: `消息删除成功`,
        key: 'action_process_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}

/**
 * 标记为已读
 */
function onMarkAsRead(row: SystemMessageType.UserMessageListVo) {
  const hideLoading = message.loading({
    content: `正在标记为已读...`,
    duration: 0,
    key: 'mark_read_msg',
  });
  markMessageAsRead(row.id!)
    .then(() => {
      message.success({
        content: `消息已标记为已读`,
        key: 'mark_read_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}

/**
 * 标记为未读
 */
function onMarkAsUnread(row: SystemMessageType.UserMessageListVo) {
  const hideLoading = message.loading({
    content: `正在标记为未读...`,
    duration: 0,
    key: 'mark_unread_msg',
  });
  markMessageAsUnRead(row.id!)
    .then(() => {
      message.success({
        content: `消息已标记为未读`,
        key: 'mark_unread_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}

function onRefresh() {
  gridApi.query();
}

/**
 * 批量删除消息
 */
function onBatchDelete() {
  const selectedRows = gridApi.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要删除的消息');
    return;
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRows.length} 条消息吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      const ids = selectedRows
        .map((row: SystemMessageType.UserMessageListVo) => {
          const id = row.id;
          return id;
        })
        .filter((id: any) => !Number.isNaN(id) && id !== undefined) as number[];

      const hideLoading = message.loading({
        content: `正在删除 ${selectedRows.length} 条消息...`,
        duration: 0,
        key: 'batch_delete_msg',
      });

      deleteMessages(ids)
        .then(() => {
          message.success({
            content: `成功删除 ${selectedRows.length} 条消息`,
            key: 'batch_delete_msg',
          });
          onRefresh();
        })
        .catch(() => {
          hideLoading();
        });
    },
  });
}

/**
 * 批量标记为已读
 */
function onBatchMarkRead() {
  const selectedRows = gridApi.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要标记的消息');
    return;
  }

  const unreadRows = selectedRows.filter(row => row.isRead === 0);
  if (unreadRows.length === 0) {
    message.warning('选中的消息都已是已读状态');
    return;
  }

  const hideLoading = message.loading({
    content: `正在批量标记为已读...`,
    duration: 0,
    key: 'batch_mark_read_msg',
  });

  Promise.all(unreadRows.map(row => markMessageAsRead(row.id!)))
    .then(() => {
      message.success({
        content: `成功标记 ${unreadRows.length} 条消息为已读`,
        key: 'batch_mark_read_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}
</script>

<template>
  <div>
    <DetailModal />
    <Page auto-content-height>
      <Grid table-title="我的消息">
        <template #toolbar-tools>
          <Button @click="onBatchMarkRead"> 批量已读 </Button>
          <span class="mx-2"></span>
          <Button danger @click="onBatchDelete"> 批量删除 </Button>
        </template>
      </Grid>
    </Page>
  </div>
</template>