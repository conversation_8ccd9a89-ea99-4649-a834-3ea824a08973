import type { VbenFormSchema } from '@vben/common-ui';
import type { VxeGridPropTypes } from '@vben/plugins/vxe-table';

import type { OnActionClickParams } from '#/adapter/vxe-table';
import type { SystemMessageType } from '#/api/system/message';

/**
 * 获取搜索表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'title',
      label: '消息标题',
      componentProps: {
        placeholder: '请输入消息标题',
      },
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: '系统消息', value: 1 },
          { label: '通知消息', value: 2 },
          { label: '公告消息', value: 3 },
        ],
        placeholder: '请选择消息类型',
      },
      fieldName: 'type',
      label: '消息类型',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: '普通', value: 1 },
          { label: '重要', value: 2 },
          { label: '紧急', value: 3 },
        ],
        placeholder: '请选择消息级别',
      },
      fieldName: 'level',
      label: '消息级别',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: '已读', value: true },
          { label: '未读', value: false },
        ],
        placeholder: '请选择读取状态',
      },
      fieldName: 'isRead',
      label: '读取状态',
    },
    {
      component: 'Input',
      fieldName: 'senderName',
      label: '发送者',
      componentProps: {
        placeholder: '请输入发送者姓名',
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: '发送时间',
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: (
    params: OnActionClickParams<SystemMessageType.UserMessageListVo>,
  ) => void,
): VxeGridPropTypes.Columns<SystemMessageType.UserMessageListVo> {
  return [
    {
      title: '消息ID',
      align: 'left',
      type: 'checkbox',
    },
    {
      field: 'title',
      title: '消息标题',
      minWidth: 200,
      showOverflow: 'tooltip',
      formatter: ({ cellValue }) => cellValue || '--',
    },
    {
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'blue', label: '系统消息', value: 1 },
          { color: 'green', label: '通知消息', value: 2 },
          { color: 'orange', label: '公告消息', value: 3 },
        ],
      },
      field: 'type',
      title: '消息类型',
      width: 100,
    },
    {
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'default', label: '普通', value: 1 },
          { color: 'warning', label: '重要', value: 2 },
          { color: 'error', label: '紧急', value: 3 },
        ],
      },
      field: 'level',
      title: '消息级别',
      width: 100,
    },
    {
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'success', label: '已读', value: 1 },
          { color: 'error', label: '未读', value: 0 },
        ],
      },
      field: 'isRead',
      title: '读取状态',
      width: 100,
    },
    {
      field: 'senderName',
      title: '发送者',
      width: 120,
      formatter: ({ cellValue }) => cellValue || '--',
    },
    {
      field: 'content',
      title: '消息内容',
      minWidth: 200,
      showOverflow: 'tooltip',
      formatter: ({ cellValue }) => cellValue || '--',
    },
    {
      field: 'createTime',
      title: '发送时间',
      width: 160,
      formatter: ({ cellValue }) => cellValue || '--',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'title',
          nameTitle: '消息',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'detail',
            text: '详情',
          },
          {
            code: 'markRead',
            text: '标记已读',
            show: ({ row }) => row.isRead === 0,
          },
          {
            code: 'markUnread',
            text: '标记未读',
            show: ({ row }) => row.isRead === 1,
          },
          {
            code: 'delete',
            text: '删除',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 200,
    },
  ];
}