<script lang="ts" setup>
import type { SystemMessageType } from '#/api/system/message';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Card, Col, Row, Tag } from 'ant-design-vue';

import { getMessageDetailById, markMessageAsRead } from '#/api/system/message';

const detailData = ref<SystemMessageType.UserMessageVo>();

const getTitle = computed(() => {
  return '消息详情';
});

/**
 * 获取消息类型标签
 */
function getTypeTag(type?: number) {
  switch (type) {
    case 1:
      return { color: 'blue', text: '系统消息' };
    case 2:
      return { color: 'green', text: '通知消息' };
    case 3:
      return { color: 'orange', text: '公告消息' };
    default:
      return { color: 'default', text: '未知' };
  }
}

/**
 * 获取消息级别标签
 */
function getLevelTag(level?: number) {
  switch (level) {
    case 1:
      return { color: 'default', text: '普通' };
    case 2:
      return { color: 'warning', text: '重要' };
    case 3:
      return { color: 'error', text: '紧急' };
    default:
      return { color: 'default', text: '未知' };
  }
}

/**
 * 加载消息详情数据
 */
async function loadMessageData(messageId: number) {
  try {
    detailData.value = await getMessageDetailById(messageId);
    // 自动标记为已读
    await markMessageAsRead(messageId);
  } catch (error) {
    console.error('获取消息详情失败:', error);
  }
}

const emit = defineEmits(['success']);

const [Drawer, drawerApi] = useVbenDrawer({
  title: '消息详情',
  class: 'w-full max-w-[800px]',
  showCancelButton: false,
  showConfirmButton: false,
  onOpenChange(isOpen) {
    if (isOpen) {
      drawerApi.setState({ loading: true });

      const data = drawerApi.getData<SystemMessageType.UserMessageListVo>();
      if (data && data.id) {
        const messageId = data.id;
        if (!Number.isNaN(messageId)) {
          loadMessageData(messageId).finally(() => {
            drawerApi.setState({ loading: false });
          });
        } else {
          drawerApi.setState({ loading: false });
        }
      } else {
        drawerApi.setState({ loading: false });
      }
    } else {
      detailData.value = undefined;
    }
  },
});

defineExpose({
  drawerApi,
});
</script>

<template>
  <Drawer>
    <div v-if="detailData" class="mx-4">
      <Card title="基本信息" size="small" class="mb-4">
        <Row :gutter="[16, 16]">
          <Col :span="12">
          <div class="flex items-center">
            <span class="mr-3 w-20 text-right text-gray-600 dark:text-gray-300">消息ID:
            </span>
            <span class="flex-1 font-medium">{{
              detailData.id || '--'
              }}</span>
          </div>
          </Col>
          <Col :span="12">
          <div class="flex items-center">
            <span class="mr-3 w-20 text-right text-gray-600 dark:text-gray-300">消息标题:
            </span>
            <span class="flex-1 font-medium">{{
              detailData.title || '--'
              }}</span>
          </div>
          </Col>
          <Col :span="12">
          <div class="flex items-center">
            <span class="mr-3 w-20 text-right text-gray-600 dark:text-gray-300">消息类型:
            </span>
            <span class="flex-1">
              <Tag :color="getTypeTag(detailData.type).color">
                {{ getTypeTag(detailData.type).text }}
              </Tag>
            </span>
          </div>
          </Col>
          <Col :span="12">
          <div class="flex items-center">
            <span class="mr-3 w-20 text-right text-gray-600 dark:text-gray-300">消息级别:
            </span>
            <span class="flex-1">
              <Tag :color="getLevelTag(detailData.level).color">
                {{ getLevelTag(detailData.level).text }}
              </Tag>
            </span>
          </div>
          </Col>
        </Row>
      </Card>

      <Card title="发送信息" size="small" class="mb-4">
        <Row :gutter="[16, 16]">
          <Col :span="12">
          <div class="flex items-center">
            <span class="mr-3 w-20 text-right text-gray-600 dark:text-gray-300">发送者:
            </span>
            <span class="flex-1 font-medium">{{
              detailData.senderName || '--'
            }}</span>
          </div>
          </Col>
          <Col :span="12">
          <div class="flex items-center">
            <span class="mr-3 w-20 text-right text-gray-600 dark:text-gray-300">发送时间:
            </span>
            <span class="flex-1 font-medium">{{
              detailData.sentTime || '--'
            }}</span>
          </div>
          </Col>
        </Row>
      </Card>

      <Card title="消息内容" size="small">
        <div class="min-h-[200px] p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
          <div class="whitespace-pre-wrap text-gray-800 dark:text-gray-200 leading-relaxed"
            v-html="detailData.content || '暂无内容'"></div>
        </div>
      </Card>
    </div>
    <div v-else class="flex-center min-h-[200px] text-gray-500">
      加载中...
    </div>
  </Drawer>
</template>
