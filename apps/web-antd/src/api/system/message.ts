import {requestClient} from '#/api/request';

export namespace SystemMessageType {
  export interface UserMessageListVo {
    /** 消息ID */
    id?: number;
    /** 消息标题 */
    title?: string;
    /** 消息类型 */
    type?: number;
    /** 消息级别 */
    level?: number;
    /** 是否已读 */
    isRead?: number;
    /** 发送者名称 */
    senderName?: string;
    /** 概要内容 */
    content?: string;
    /** 创建时间(发送时间) */
    createTime?: string;
  }

  export interface UserMessageListQueryRequest {
    /** 消息标题 */
    title?: string;
    /** 消息类型：1-系统消息 2-通知消息 3-公告消息 */
    type?: number;
    /** 消息级别：1-普通 2-重要 3-紧急 */
    level?: number;
    /** 是否已读 */
    isRead?: boolean;
    /** 发送者姓名 */
    senderName?: string;
  }

  export interface UserMessageVo {
    /** 消息ID */
    id?: number;
    /** 消息标题 */
    title?: string;
    /** 消息内容 */
    content?: string;
    /** 消息类型：1-系统消息 2-通知消息 3-公告消息 */
    type?: number;
    /** 消息级别：1-普通 2-重要 3-紧急 */
    level?: number;
    /** 发送者姓名 */
    senderName?: string;
    /** 发送时间 */
    sentTime?: string;
  }

  export interface UserMessageReadCountDto {
    /** 总数量 */
    total?: number;
    /** 已读数量 */
    read?: number;
    /** 未读数量 */
    unRead?: number;
  }
}

/**
 *  获取用户消息列表
 * @param params 查询参数
 */
async function listUserMessageList(
  params?: SystemMessageType.UserMessageListQueryRequest,
) {
  return requestClient.get<SystemMessageType.UserMessageListVo>('/system/message/list', {params});
}

/**
 * 获取消息详情
 * @param id 消息ID
 */
async function getMessageDetailById(id: number) {
  return requestClient.get<SystemMessageType.UserMessageVo>(
    `/system/message/${id}`,
  );
}

/**
 * 获取未读消息数量
 */
async function getUnreadCount() {
  return requestClient.get<SystemMessageType.UserMessageReadCountDto>(
    '/system/message/count',
  );
}

/**
 * 标记消息为已读
 * @param id 消息ID
 */
async function markMessageAsRead(id: number) {
  return requestClient.put(`/system/message/read/${id}`);
}

/**
 * 标记消息为未读
 * @param id 消息ID
 */
async function markMessageAsUnRead(id: number) {
  return requestClient.put(`/system/message/unRead/${id}`);
}

/**
 * 删除消息 (支持批量删除)
 * @param ids 消息ID列表
 */
async function deleteMessages(ids: Array<number>) {
  return requestClient.delete(`/system/message/delete/${ids.join(',')}`);
}

export {
  deleteMessages,
  getMessageDetailById,
  getUnreadCount,
  listUserMessageList,
  markMessageAsRead,
  markMessageAsUnRead,
};
