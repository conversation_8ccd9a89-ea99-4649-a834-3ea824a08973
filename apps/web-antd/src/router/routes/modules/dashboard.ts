import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: $t('page.dashboard.title'),
    },
    name: 'Dashboard',
    path: '/dashboard',
    children: [
      {
        name: 'Analytics',
        path: '/analytics',
        component: () => import('#/views/dashboard/analytics/index.vue'),
        meta: {
          affixTab: true,
          icon: 'lucide:area-chart',
          title: $t('page.dashboard.analytics'),
        },
      },
      {
        name: 'Workspace',
        path: '/workspace',
        component: () => import('#/views/dashboard/workspace/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: $t('page.dashboard.workspace'),
        },
      },
      {
        name: 'Message',
        path: '/message',
        component: () => import('#/views/dashboard/message/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: '我的消息',
        },
        children: [
          {
            name: 'MessageList',
            path: '/message/:id',
            component: () =>
              import('#/views/dashboard/message/modules/detail.vue'),
            meta: {
              affixTab: true,
              icon: 'carbon:workspace',
              title: '消息详情',
            },
          },
        ],
      },
    ],
  },
];

export default routes;
